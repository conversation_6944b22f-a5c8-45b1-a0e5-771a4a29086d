<template>
  <div class="IndexBox">
    <!-- 头部背景区域 -->
    <div class="header-bg">
      <!-- 用户信息区域 -->
      <div class="user-section">
        <div class="user-info">
          <div class="avatar-container">
            <img
              class="avatar"
              :src="`./static/head/${UserInfo.header}`"
              alt="avatar"
            />
          </div>
          <div class="user-details">
            <div class="account-name">
              {{ $t("user.myPage.account") }}:{{ UserInfo.username || "--" }}
            </div>
            <div class="invitation-code">
              {{ $t("user.myPage.inviteCode") }}: {{ UserInfo.idcode || "--" }}
            </div>
          </div>
        </div>
        <button @click="$Model.Logout()" class="exit-btn">
          {{ $t("user.myPage.logout") }}
        </button>
      </div>

      <!-- 余额卡片 -->
      <div class="balance-card">
        <div class="balance-content">
          <span class="balance-label">{{ $t("user.myPage.balance") }}</span>
          <span
            class="amount"
            :title="formatBalanceTooltip(UserInfo.balance)"
            >{{ formatBalanceDisplay(UserInfo.balance) }}</span
          >
          <span class="currency">{{ $Currency.getSymbol() }}</span>
          <button class="wallet-btn" @click="$router.push('/user/wallet')">
            {{ $t("user.myPage.myWallet") }}
          </button>
        </div>
      </div>

      <!-- 充值提现按钮区域 -->
      <div class="action-buttons">
        <button class="action-btn recharge-btn" @click="goToRecharge">
          <div class="btn-icon">
            <img src="@/static/icon/recharge.png" alt="充值" />
          </div>
          <span class="btn-text">{{ $t("wallet.default[1]") }}</span>
        </button>
        <button class="action-btn withdraw-btn" @click="goToWithdraw">
          <div class="btn-icon">
            <img src="@/static/icon/Withdrawal.png" alt="提现" />
          </div>
          <span class="btn-text">{{ $t("wallet.default[2]") }}</span>
        </button>
      </div>
    </div>

    <div class="content-area">
      <!-- 统计卡片 -->
      <div class="stats-section">
        <div class="stats-grid">
          <div
            class="stat-card earnings-item"
            @click="
              showEarningsDetail(
                'totalProfit',
                statisticalData.total_profit,
                $t('user.myPage.totalEarnings')
              )
            "
          >
            <div class="stat-label">{{ $t("user.myPage.totalEarnings") }}</div>
            <div
              class="stat-value"
              :title="formatBalanceTooltip(statisticalData.total_profit)"
            >
              {{ formatBalanceDisplay(statisticalData.total_profit) }}
            </div>
          </div>
          <div
            class="stat-card earnings-item"
            @click="
              showEarningsDetail(
                'todayEarnings',
                statisticalData.today_earnings,
                $t('user.myPage.todayRemaining')
              )
            "
          >
            <div class="stat-label">{{ $t("user.myPage.todayRemaining") }}</div>
            <div
              class="stat-value"
              :title="formatBalanceTooltip(statisticalData.today_earnings)"
            >
              {{ formatBalanceDisplay(statisticalData.today_earnings) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 功能菜单区域 -->
      <div class="menu-section">
        <div class="menu-grid">
          <div class="menu-item" @click="$router.push('/user/info')">
            <div class="menu-icon personal-icon">
              <img
                src="@/static/icon/personalinformation.png"
                :alt="$t('user.menu[3]')"
              />
            </div>
            <span
              class="menu-text"
              v-html="$t('user.menu[3]').replace('个人', '个人<br>')"
            ></span>
          </div>
          <div class="menu-item" @click="$router.push('/myTask')">
            <div class="menu-icon task-icon">
              <img
                src="@/static/icon/taskRececord.png"
                :alt="$t('user.menu[0]')"
              />
            </div>
            <span
              class="menu-text"
              v-html="$t('user.menu[0]').replace('记录', '<br>记录')"
            ></span>
          </div>
          <!-- <div class="menu-item" @click="$router.push('/user/dayReport')">
            <div class="menu-icon daily-icon">
              <img src="@/static/icon/dailyStatement.png" :alt="$t('user.menu[5]')" />
            </div>
            <span class="menu-text" v-html="$t('user.menu[5]').replace('日结', '日结<br>')"></span>
          </div> -->
          <div class="menu-item" @click="$router.push('/user/fundRecord')">
            <div class="menu-icon account-icon">
              <img
                src="@/static/icon/AccountRecords.png"
                :alt="$t('user.menu[6]')"
              />
            </div>
            <span
              class="menu-text"
              v-html="$t('user.menu[6]').replace('账变', '账变<br>')"
            ></span>
          </div>
          <div class="menu-item" @click="$router.push('/user/teamReport')">
            <div class="menu-icon team-icon">
              <img
                src="@/static/icon/TeamReports.png"
                :alt="$t('user.menu[8]')"
              />
            </div>
            <span
              class="menu-text"
              v-html="$t('user.menu[8]').replace('团队', '团队<br>')"
            ></span>
          </div>
          <div class="menu-item" @click="$router.push('/help')">
            <div class="menu-icon help-icon">
              <img src="@/static/icon/Helpbook.png" :alt="$t('user.menu[9]')" />
            </div>
            <span class="menu-text">{{ $t("user.menu[9]") }}</span>
          </div>
          <div
            class="menu-item"
            @click="$router.push('/user/credit')"
            v-if="shouldShowCreditCenter"
          >
            <div class="menu-icon credit-icon">
              <img
                src="@/static/icon/creditCentres.png"
                :alt="$t('user.menu[10]')"
              />
            </div>
            <span
              class="menu-text"
              v-html="$t('user.menu[10]').replace('信用', '信用<br>')"
            ></span>
          </div>
          <div class="menu-item" @click="$router.push('/user/wheel')">
            <div class="menu-icon wheel-icon">
              <img
                src="@/static/icon/wheeloffortune.png"
                :alt="$t('user.myPage.luckyDraw')"
              />
            </div>
            <span class="menu-text">{{ $t("user.myPage.luckyDraw") }}</span>
          </div>
          <div class="menu-item" @click="$router.push('/appDown')">
            <div class="menu-icon app-download-icon">
              <img
                src="@/static/icon/download.png"
                :alt="$t('user.menu[11]')"
              />
            </div>
            <span
              class="menu-text"
              v-html="$t('user.menu[11]').replace('APP', 'APP<br>')"
            ></span>
          </div>
        </div>
      </div>

      <!-- 抽奖区域 -->
      <!-- <div class="invite-section">
        <div class="invite-item" @click="$router.push('/user/wheel')">
          <div class="invite-icon">
            <img src="@/static/icon/Frame.png" :alt="$t('user.myPage.luckyDraw')" />
          </div>
          <span class="invite-text">{{ $t('user.myPage.luckyDraw') }}</span>
          <van-icon name="arrow" class="invite-arrow" />
        </div>
      </div> -->

      <!-- 邀请好友区域 -->
      <div class="invite-banner" @click="inviteFriends">
        <div class="invite-content">
          <div class="invite-text-section">
            <h3 class="invite-title">{{ $t("user.inviteBanner.title") }}</h3>
            <div class="invite-subtitle-wrapper">
              <p class="invite-subtitle">
                {{ $t("user.inviteBanner.subtitle") }}
              </p>
              <div class="invite-arrow-icon">
                <img
                  src="@/static/icon/Frame1.png"
                  :alt="$t('user.inviteBanner.subtitle')"
                />
              </div>
            </div>
          </div>
          <!-- 由于背景图片已经包含了手机和金币的设计，这里可以移除图片或保留作为额外装饰 -->
          <!-- <div class="invite-image-section">
            <img src="@/static/images/money.png" alt="邀请好友" class="invite-phone-image" />
          </div> -->
        </div>
      </div>
    </div>

    <Footer />
  </div>
</template>

<script>
import Footer from "@/components/Footer.vue";
import CurrencyFormatMixin from "@/mixins/CurrencyFormatMixin.js";

export default {
  name: "User",
  mixins: [CurrencyFormatMixin],
  components: {
    Footer,
  },
  props: [],
  data() {
    return {
      statisticalData: {},
    };
  },
  computed: {
    shouldShowCreditCenter() {
      // 1=显示信用界面, 2=隐藏信用界面
      return this.InitData && this.InitData.show_credit_interface === 1;
    },
  },
  watch: {},
  created() {
    console.log("=== 个人页面初始化 ===");

    // 获取统计数据
    this.$Model.GetStatisticsInfo((data) => {
      console.log("获取统计数据响应:", data);
      if (data.code == 1) {
        this.statisticalData = data.info;
        console.log("统计数据获取成功:", data.info);
      } else {
        console.log("统计数据获取失败:", data.code_dec);
      }
    });
  },
  mounted() {},
  activated() {},
  destroyed() {},
  methods: {
    goDown() {
      const url = "/upload/apk/SmartNest_CN.apk";
      console.log(url);
      // 下载
      const a = document.createElement("a");
      a.href = url;
      a.download = "SmartNest_CN.apk";
      a.click();
    },
    signin() {
      this.$Model.signin({}, (data) => {
        if (data.code == 1) {
          this.$Model.GetUserInfo();
        }
      });
    },
    baozhengjin() {
      this.$Model.baozhengjin({}, (data) => {});
    },
    inviteFriends() {
      // 邀请好友功能
      this.$router.push("/user/promote");
    },

    // 跳转到充值页面
    goToRecharge() {
      this.$router.push("/user/watchPay");
    },

    // 跳转到提现功能（钱包页面的提现功能）
    goToWithdraw() {
      this.$router.push("/user/wallet");
    },

    // 显示收益详情弹窗
    showEarningsDetail(type, value, label) {
      console.log("显示收益详情:", type, value, label);
      const fullValue = `${this.$Currency.getSymbol()}${(value || 0).toFixed(
        2
      )}`;
      this.$Dialog.Alert(
        `${this.$t("dialog[7]") || "完整金额"}：${fullValue}`,
        () => {},
        this.$t("dialog[1]") || "确定",
        label
      );
    },
  },
};
</script>
<style scoped>
.IndexBox {
  background: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 75px;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 头部背景区域 */
.header-bg {
  background: linear-gradient(180deg, #f42937 0%, rgba(244, 41, 55, 0) 100%);
  padding: 0 0 20px 0;
  position: relative;
  height: auto;
  min-height: 200px;
}

/* 用户信息区域 */
.user-section {
  padding: 20px 20px 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar-container {
  margin-right: 16px;
}

.avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  object-fit: cover;
}

.user-details {
  flex: 1;
}

.account-name {
  color: white;
  font-family: "PingFang SC", sans-serif;
  font-weight: 600;
  font-size: 16px;
  margin: 0 0 4px 0;
  line-height: 22px;
}

.invitation-code {
  color: rgba(255, 255, 255, 0.9);
  font-family: "PingFang SC", sans-serif;
  font-weight: 400;
  font-size: 14px;
  margin: 0;
  line-height: 20px;
}

.exit-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 8px 16px;
  font-family: "PingFang SC", sans-serif;
  font-weight: 400;
  font-size: 12px;
  cursor: pointer;
  line-height: 16px;
  backdrop-filter: blur(10px);
}

/* 余额卡片 */
.balance-card {
  margin: 0 24px 0;
  background: #ffffff;
  border-radius: 0 0 12px 12px;
  padding: 21.5px 12px;
  position: relative;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.balance-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 36px;
  border-radius: 4px;
  gap: 8px;
  padding: 0;
}

.balance-label {
  color: #292929;
  font-family: "Source Han Sans SC", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
}

.amount {
  color: #292929;
  font-family: "Source Han Sans CN", sans-serif;
  font-weight: 700;
  font-size: 24px;
  line-height: 36px;
  white-space: nowrap;
}

.currency {
  color: #292929;
  font-family: "Source Han Sans SC", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  white-space: nowrap;
}

.wallet-btn {
  background: #ff0f23;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 7px 12px;
  font-family: "Source Han Sans CN", sans-serif;
  font-weight: 500;
  font-size: 10px;
  cursor: pointer;
  line-height: 15px;
  margin-left: auto;
  white-space: nowrap;
  min-width: 46px;
}

/* 充值提现按钮区域 */
.action-buttons {
  display: flex;
  gap: 12px;
  margin: 16px 24px 0;
  padding: 0;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-family: "Source Han Sans CN", sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  min-height: 48px;
}

.recharge-btn {
  background: linear-gradient(135deg, #ff8f8f 0%, #ff0f23 100%);
  box-shadow: 0 2px 8px rgba(255, 15, 35, 0.3);
}

.recharge-btn:hover {
  background: linear-gradient(135deg, #ff0f23 0%, #e60e20 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 15, 35, 0.4);
}

.withdraw-btn {
  background: linear-gradient(135deg, #ff8f8f 0%, #ff0f23 100%);
  box-shadow: 0 2px 8px rgba(255, 15, 35, 0.3);
}

.withdraw-btn:hover {
  background: linear-gradient(135deg, #ff0f23 0%, #e60e20 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 15, 35, 0.4);
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.btn-icon img {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1); /* 将图标变为白色 */
}

.btn-text {
  font-weight: 500;
  line-height: 20px;
}

/* 内容区域 */
.content-area {
  background: #f8f9fa;
  margin-top: 0;
  width: 100%;
  height: 0;
  padding-bottom: 20px;
  flex: 1;
  overflow-y: auto;
}

/* 统计区域 */
.stats-section {
  margin: 0 24px 16px;
}

.stats-grid {
  display: flex;
  gap: 15px;
  margin-bottom: 0;
}

.stat-card {
  flex: 1;
  background: #ffffff;
  border-radius: 12px;
  padding: 20px 16px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

/* 收益项点击效果 */
.earnings-item {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.earnings-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.earnings-item:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-label {
  color: #7d7d7d;
  font-family: "Source Han Sans SC", sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  margin-bottom: 8px;
  text-align: center;
}

.stat-value {
  color: #615e5e;
  font-family: "Source Han Sans CN", sans-serif;
  font-weight: 500;
  font-size: 20px;
  line-height: 30px;
  text-align: center;
}

/* 菜单区域 */
.menu-section {
  margin: 0 24px 16px;
  background: #ffffff;
  border-radius: 12px;
  padding: 20px 16px;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px 8px;
  justify-items: center;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 4px;
  width: 60px;
}

.menu-icon {
  width: 40px;
  height: 40px;
  background: transparent;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
}

/* 所有图标使用透明背景 */
.task-icon,
.personal-icon,
.daily-icon,
.account-icon,
.team-icon,
.help-icon,
.credit-icon,
.wheel-icon,
.app-download-icon {
  background: transparent;
}

.menu-icon >>> .van-icon {
  font-size: 16px;
  color: white;
}

.menu-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.menu-text {
  color: #333333;
  font-family: "PingFang SC", sans-serif;
  font-weight: 400;
  font-size: 10px;
  text-align: center;
  line-height: 14px;
  width: 60px;
  word-wrap: break-word;
}

/* 邀请好友横幅区域 */
.invite-banner {
  margin: 0 24px 16px;
  background-image: url("~@/static/images/Frame 3.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  min-height: 80px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.invite-banner:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.invite-content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 18px 20px;
  position: relative;
  z-index: 2;
  height: 100%;
}

.invite-text-section {
  flex: 1;
  color: #333;
}

.invite-title {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 6px 0;
  color: #333;
  font-family: "PingFang SC", sans-serif;
}

.invite-subtitle-wrapper {
  display: flex;
  align-items: center;
}

.invite-subtitle {
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  color: #666;
  font-family: "PingFang SC", sans-serif;
}

.invite-arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-left: 8px;
  flex-shrink: 0;
}

.invite-arrow-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 图片相关样式已移除，因为使用背景图片 */
</style>
