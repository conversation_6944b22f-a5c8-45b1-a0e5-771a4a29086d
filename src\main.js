import "jquery";
import Vue from "vue";
import App from "@/App.vue";
import router from "@/router";
import store from "@/store";

import Vant, { Locale, ImagePreview } from "vant";

import Model from "@/common/Model";
import Dialog from "@/common/Dialog";
import Util from "@/common/Util";
import UserManager from "@/common/UserManager";
import CurrencyManager from "@/common/CurrencyManager";
import CurrencyMixin from "@/mixins/CurrencyMixin";
import CryptoManager from "@/common/CryptoManager";

import i18n, { SetLanguage } from "./i18n";

import "vant/lib/index.css";
import "@/assets/css/style.css";

/*APP*/
Vue.use(Vant);
Vue.mixin(CurrencyMixin);
Vue.prototype.$SetLanguage = SetLanguage;
Vue.prototype.$ImagePreview = ImagePreview;
Vue.prototype.$Model = Model;
Vue.prototype.$Dialog = Dialog;
Vue.prototype.$Util = Util;
Vue.prototype.$UserManager = UserManager;
Vue.prototype.$Currency = CurrencyManager;
Vue.prototype.$AppInit = false;
Vue.prototype.$domWidth = document.documentElement.clientWidth;
Vue.prototype.$langList = langList;

// 定义 $getIcon 方法，返回正常的静态资源路径
Vue.prototype.$getIcon = function(iconName) {
  return `./static/icon/${iconName}`;
};

Vue.config.productionTip = false;

Vue.config.devtools = false;

import Footer from "@/components/Footer";

Vue.component("Footer", Footer);

// 将Model和axios暴露到全局，供CryptoManager使用
window.Model = Model;
window.axios = require("@/common/Axios").default;

// 将CryptoManager挂载到Vue原型上
Vue.prototype.$CryptoManager = CryptoManager;

// 先初始化加密管理器，再获取后台数据
async function initializeApp() {
  try {
    console.log('开始初始化加密管理器...');
    CryptoManager.init();
    
    // 等待加密管理器就绪
    await CryptoManager.waitForReady(15000);
    console.log('加密管理器初始化完成，开始获取后台数据');
    
    // 获取后台数据（不需要加密的基础数据）
    Model.GetBackData();
  } catch (error) {
    console.log('加密管理器初始化失败:', error);
    // 即使失败也继续，可能是不需要加密的接口
    Model.GetBackData();
  }
}

// 立即开始初始化
initializeApp();
/*路由登录逻辑*/
router.beforeEach(async (to, from, next) => {
  if (from.name == "line") {
    Model.GetBackData();
  }
  if (from.name != "login" && from.name != "register") {
    localStorage["FromPage"] = from.fullPath;
  } else {
    localStorage.removeItem("FromPage");
  }

  // 检查是否需要登录验证
  if (
    to.name != "login" &&
    !localStorage["Token"] &&
    to.matched.some((record) => record.meta.requiresAuth)
  ) {
    next("/login");
    return;
  }

  // 如果有Token但没有用户信息，才获取用户信息
  if (localStorage["Token"] && to.name !== "login" && !store.state.UserInfo) {
    console.log("=== 路由守卫检测到Token但无用户信息，获取用户信息 ===");
    console.log("目标路由:", to.name);
    console.log("Token存在:", !!localStorage["Token"]);
    console.log("用户信息存在:", !!store.state.UserInfo);

    try {
      // 等待加密管理器准备就绪
      console.log("等待加密管理器就绪...");
      await CryptoManager.waitForReady(10000);
      console.log("加密管理器已就绪，开始获取用户信息");
      
      await new Promise((resolve, reject) => {
        Model.GetUserInfo((data) => {
          console.log("路由守卫获取用户信息响应:", data);
          if (data.code === 1) {
            console.log("路由守卫获取用户信息成功");
            resolve(data);
          } else {
            console.log("路由守卫获取用户信息失败:", data.code_dec);
            reject(data);
          }
        });
      });
    } catch (error) {
      console.log("路由守卫获取用户信息失败:", error);
      // 如果获取用户信息失败，清除Token并跳转到登录页
      localStorage.removeItem("Token");
      localStorage.removeItem("UserInfo");
      store.dispatch("UpdateUserInfo", "");
      if (to.matched.some((record) => record.meta.requiresAuth)) {
        next("/login");
        return;
      }
    }
  } else if (
    localStorage["Token"] &&
    to.name !== "login" &&
    store.state.UserInfo
  ) {
    console.log("=== 路由守卫检测到Token和用户信息都存在，跳过获取 ===");
    console.log("目标路由:", to.name);
  }

  next();
});

new Vue({
  router,
  store,
  i18n,
  render: (h) => h(App),
}).$mount("#app");
