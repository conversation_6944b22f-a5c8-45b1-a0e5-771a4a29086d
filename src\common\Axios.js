import axios from 'axios'
import store from '@/store'
import router from '@/router'
import { Toast,Dialog } from 'vant'
import i18n from '@/i18n'
import CryptoManager from './CryptoManager'

// 请求队列管理
class RequestQueue {
  constructor() {
    this.queue = []
    this.processing = false
    
    // 不加密白名单 - 只有获取公钥接口不需要加密
    this.noEncryptWhitelist = [
      '/getRsaPublicKey',     // 获取RSA公钥
      'getRsaPublicKey',      // 兼容不同的URL格式
    ]
  }
  
  // 检查接口是否在白名单中
  isInWhitelist(apiPath) {
    return this.noEncryptWhitelist.some(path => 
      apiPath.includes(path) || apiPath.endsWith(path)
    )
  }
  
  // 添加请求到队列
  addToQueue(config, resolve, reject) {
    console.log('[RequestQueue] 添加请求到队列:', config.url)
    this.queue.push({ config, resolve, reject, timestamp: Date.now() })
    
    // 启动队列处理（如果还没在处理中）
    if (!this.processing) {
      this.processQueue()
    }
  }
  
  // 处理队列中的请求
  async processQueue() {
    if (this.processing || this.queue.length === 0) return
    
    this.processing = true
    console.log('[RequestQueue] 开始处理请求队列，队列长度:', this.queue.length)
    
    try {
      // 等待加密管理器准备就绪
      await CryptoManager.waitForReady(15000)
      console.log('[RequestQueue] 加密管理器就绪，开始处理队列请求')
      
      // 处理队列中的所有请求
      const currentQueue = [...this.queue]
      this.queue = [] // 清空队列
      
      for (const item of currentQueue) {
        try {
          console.log('[RequestQueue] 处理队列请求:', item.config.url)
          
          // 确保method字段存在
          item.config.method = item.config.method || 'POST';
          
          // 重新处理这个请求（会经过请求拦截器重新加密）
          const response = await instance.request(item.config)
          item.resolve(response)
          
        } catch (error) {
          console.error('[RequestQueue] 队列请求处理失败:', item.config.url, error)
          item.reject(error)
        }
      }
      
    } catch (error) {
      console.error('[RequestQueue] 队列处理失败:', error)
      
      // 处理失败时，拒绝所有队列中的请求
      const currentQueue = [...this.queue]
      this.queue = []
      
      currentQueue.forEach(item => {
        item.reject(new Error('加密系统初始化失败，请求无法完成'))
      })
      
    } finally {
      this.processing = false
      
      // 如果处理过程中又有新请求加入，继续处理
      if (this.queue.length > 0) {
        setTimeout(() => this.processQueue(), 100)
      }
    }
  }
}

// 创建请求队列实例
const requestQueue = new RequestQueue()

//接口配置

var instance = axios.create({
  baseURL: (localStorage['CurrLine']||ApiUrl),
  method: 'POST', // 设置默认请求方法
  headers: {
    'Content-Type':'application/x-www-form-urlencoded'
  }
})

// 强制设置axios默认配置，确保method总是存在
instance.defaults.method = 'POST';
axios.defaults.method = 'POST';

// 重写post方法确保method字段
const originalPost = instance.post;
instance.post = function(url, data, config = {}) {
  config.method = 'POST';
  return originalPost.call(this, url, data, config);
};

// 重写request方法，最后一道防线
const originalRequest = instance.request;
instance.request = function(config) {
  if (!config) config = {};
  if (!config.method || typeof config.method !== 'string') {
    config.method = 'POST';
    console.warn('[Axios] 最终防护：强制设置method为POST', config.url);
  }
  return originalRequest.call(this, config);
};

/*取消请求*/
var source = axios.CancelToken.source();

// 添加请求拦截器
instance.interceptors.request.use(
  async config => {
    // 在发送请求之前做些什么
    
    // 最优先确保method存在 - 多重防护
    if (!config.method || typeof config.method !== 'string') {
      config.method = 'POST';
      console.warn('[Axios] method字段缺失或无效，已设置为POST:', config.url);
    }
    
    if(!config.diyApi){
      config.baseURL = (localStorage['CurrLine']||ApiUrl)+'/api/';
    }else{
      config.baseURL = localStorage['UploadApi']+'/api/';
    }
    config.cancelToken = source.token;
    config.data = config.data || {};
    config.data.lang = localStorage['Language']|| Language;
    if(!config.noLogin){
      config.data.token = localStorage['Token'];
    }

    // 保存原始数据备份（用于会话过期重试）
    config._originalData = JSON.parse(JSON.stringify(config.data));

    // 检查是否需要加密
    const apiPath = config.url || '';
    const shouldEncrypt = CryptoManager.shouldEncrypt(apiPath) &&
                         !config.noEncrypt &&
                         !config.fromData && // 文件上传不加密
                         !config.noLogin;    // 无需登录的接口通常不加密

    if (shouldEncrypt) {
      // 检查是否在白名单中
      if (requestQueue.isInWhitelist(apiPath)) {
        console.log('[Axios] 白名单接口，跳过加密直接发送:', apiPath)
      } else {
        // 检查加密管理器是否就绪
        const cryptoStatus = CryptoManager.getStatus()
        if (!cryptoStatus.isReady) {
          console.log('[Axios] 加密管理器未就绪，请求加入队列:', apiPath)
          
          // 返回一个Promise，将在队列处理时resolve
          return new Promise((resolve, reject) => {
            requestQueue.addToQueue(config, resolve, reject)
          })
        }
        
        try {
          // 🚀 详细日志：会话制加密请求信息
          console.log('🚀 [Axios] 准备发送会话制加密请求')
          console.log('- API路径:', apiPath)
          console.log('- 请求方法:', (config.method && config.method.toUpperCase()) || 'POST')
          console.log('- 请求URL:', config.url)
          console.log('- 原始数据(加密前):', JSON.stringify(config.data, null, 2))

          // 对数据进行会话制加密
          const encryptedResult = await CryptoManager.encryptData(config.data);

          if (encryptedResult.encrypted) {
            // 使用加密数据
            config.data = encryptedResult;
            console.log('[Axios] 请求已进行会话制加密:', apiPath);
            console.log('- Session ID:', encryptedResult.session_id);
          } else {
            // 加密失败，抛出错误而不是降级
            const errorMsg = encryptedResult.error || '会话制加密失败';
            throw new Error(`会话制加密失败: ${errorMsg}`);
          }
        } catch (error) {
          console.error('[Axios] 会话制加密过程异常:', error);
          // 如果是超时或加密失败，直接抛出错误
          throw new Error(`请求会话制加密失败: ${error.message}`);
        }
      }
    }

    if(!config.fromData){
      config.data = $.param(config.data);
    }
    return config;
  },
  error => {
  // 对请求错误做些什么
  return Promise.reject(error);
  }
)

// 添加响应拦截器
instance.interceptors.response.use(
  async response => {
    // 对响应数据做点什么
    var Api = response.request.responseURL.slice(response.request.responseURL.lastIndexOf('\/'));
    
    // 检查会话密钥过期错误
    if (response.data.error === "SESSION_KEY_EXPIRED" && response.data.need_reestablish) {
      console.log('[Axios] 检测到会话密钥过期，自动重建会话并重试请求');
      
      try {
        // 1. 重新建立会话
        await CryptoManager.establishSession();
        console.log('[Axios] 会话重建成功，准备重试原请求');
        
        // 2. 重试原请求
        const originalConfig = response.config;
        
        // 恢复原始数据并重新加密
        if (originalConfig._originalData) {
          console.log('[Axios] 使用原始数据重新加密并重试');
          
          // 恢复原始数据
          originalConfig.data = originalConfig._originalData;
          
          // 确保method字段存在
          originalConfig.method = originalConfig.method || 'POST';
          
          // 移除_originalData避免重复处理
          delete originalConfig._originalData;
          
          // 重试请求（会自动经过请求拦截器重新加密）
          console.log('[Axios] 重试原请求:', originalConfig.url, 'method:', originalConfig.method);
          return instance.request(originalConfig);
        } else {
          console.warn('[Axios] 未找到原始数据备份，无法重试加密请求');
          return response;
        }
        
      } catch (error) {
        console.error('[Axios] 会话重建或重试失败:', error);
        // 如果重建失败，返回原错误响应
        return response;
      }
    }
    
    // 用户登录过期处理（200-206范围）
    if (response.data.code > 200 && response.data.code < 206) {
      localStorage.removeItem('Token');
      localStorage.removeItem('UserInfo');
      localStorage.removeItem('BankCardList');
      store.dispatch('UpdateUserInfo', '');
      store.dispatch('UpdateBankCardList', []);
      Toast.clear();
      if(router.history.current.name=='vip'){
        return response
      }
      source.cancel();
      Dialog.alert({
        title: i18n.t('dialog[0]'),
        message: response.data.code_dec,
        closeOnPopstate: true,
      }).then(() => {
        router.push('/login');
        source = axios.CancelToken.source();
      }).catch(()=>{
        router.push('/login');
        source = axios.CancelToken.source();
      });
    }
    return response;
  }, 
  error => {
    // 对响应错误做点什么
    let errorMessage = '';

    if (error.response && error.response.data) {
      // 检查是否是数据库错误
      if (typeof error.response.data === 'string' && error.response.data.includes('trial time')) {
        errorMessage = '系统维护中，请稍后再试';
      } else {
        // 尝试从HTML响应中提取错误信息
        const htmlMatch = /<h1>(.*?)<\/h1>/ig.exec(error.response.data);
        if (htmlMatch && htmlMatch[1]) {
          errorMessage = htmlMatch[1];
        } else {
          errorMessage = '网络请求失败，请稍后重试';
        }
      }
    } else {
      errorMessage = '网络连接失败，请检查网络设置';
    }

    Toast({message: errorMessage});
    return Promise.reject(error);
  }
)

export default instance