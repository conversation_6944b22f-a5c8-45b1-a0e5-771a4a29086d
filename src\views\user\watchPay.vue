<template>
  <div class="PageBox watch-pay-page">
    <!-- 导航栏 -->
    <van-nav-bar
      fixed
      :border="false"
      :title="$t('recharge.default[0]')"
      left-arrow
      @click-left="goBack"
      class="watch-pay-navbar"
    />

    <div class="ScrollBox WatchPay">
      <!-- 金额显示卡片 -->
      <div class="amount-display-card">
        <div class="amount-label">{{ $t('user.default[4]') }}</div>
        <div class="amount-value">RP{{ UserInfo.balance || 0 }}</div>
        <div class="amount-subtitle">{{ $t('globalPay.description') }}</div>
      </div>



      <!-- 支付方式选择 -->
      <div class="payment-section">
        <div class="section-title">{{ $t('globalPay.selectPaymentMethod') }}</div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="loading-container">
          <van-loading type="spinner" color="#FF0F23">{{ $t('recharge.default[8]') }}</van-loading>
        </div>

        <!-- 支付方式列表 -->
        <div v-else-if="paymentMethods.length > 0" class="payment-methods-scroll">
          <div
            v-for="method in paymentMethods"
            :key="`${method.code}_${method.recharge_id}`"
            class="payment-method-item"
            :class="{ active: selectedPaymentMethod === `${method.code}_${method.recharge_id}` }"
            @click="selectPaymentMethod(`${method.code}_${method.recharge_id}`)"
          >
            <div class="method-check" v-if="selectedPaymentMethod === `${method.code}_${method.recharge_id}`">✓</div>
            <div class="method-name">{{ method.name }}</div>
          </div>
        </div>

        <!-- 无支付方式提示 -->
        <div v-else class="no-payment-methods">
          <div class="no-methods-text">{{ $t('vanPull[1]') }}</div>
        </div>
      </div>

      <!-- 银行选择 -->
      <!-- <div class="bank-selection-section" v-if="selectedPaymentMethod && requiresBankCode">
        <div class="section-title">{{ $t('globalPay.selectBank') }}</div>
        <div class="bank-selector" @click="showBankPicker = true">
          <span class="selected-bank" v-if="selectedBankCode">{{ selectedBankName }}</span>
          <span class="placeholder" v-else>{{ $t('globalPay.selectBank') }}</span>
          <span class="arrow-icon">▼</span>
        </div>
      </div> -->

      <!-- 支付金额输入 -->
      <div class="amount-section" v-if="selectedPaymentMethod">
        <div class="section-title">{{ $t('recharge.info[0]') }}</div>

        <div class="amount-input-container">
          <div class="currency-prefix">RP</div>
          <input
            v-model="paymentAmount"
            type="number"
            :placeholder="$t('recharge.placeholder[0]')"
            class="amount-input"
            @input="onAmountChange"
          />
        </div>

        <div class="amount-range-info" v-if="selectedPaymentMethod">
          {{ getAmountRangeText() }}
        </div>
      </div>

      <!-- 支付按钮 -->
      <div class="payment-button-section" v-if="selectedPaymentMethod && paymentAmount">
        <button
          class="pay-now-button"
          @click="createPaymentOrder"
        >
          {{ $t('recharge.default[6]') }} RP {{ formatDisplayAmount(paymentAmount) }}
        </button>
      </div>
    </div>

    <!-- 银行选择弹窗 -->
    <!-- <van-popup
      v-model="showBankPicker"
      position="bottom"
      :style="{ height: '40%' }"
      class="PickerPopup"
    >
      <van-picker
        show-toolbar
        :columns="bankList"
        @confirm="onBankConfirm"
        @cancel="showBankPicker = false"
        :title="$t('globalPay.selectBank')"
      />
    </van-popup> -->
  </div>
</template>

<script>
import CurrencyFormatMixin from '@/mixins/CurrencyFormatMixin';

export default {
  name: "WatchPay",
  mixins: [CurrencyFormatMixin],
  data() {
    return {
      selectedPaymentMethod: null,
      paymentAmount: '',
      currentLanguage: 'en',
      paymentMethods: [],
      isLoading: true,
      selectedCountry: 'ID', // 默认印尼
      selectedBankCode: '', // 选择的银行代码
      bankList: [], // 银行列表
      showBankPicker: false, // 控制银行选择器显示
      lastClickTime: 0 // 防抖用的时间戳

    };
  },
  
  computed: {
    UserInfo() {
      return this.$store.state.UserInfo || {};
    },

    // 获取InitData中的银行列表
    InitData() {
      return this.$store.state.InitData || {};
    },

    // 当前选择的支付方式详情
    selectedPaymentMethodDetails() {
      if (!this.selectedPaymentMethod || !this.paymentMethods.length) {
        return null;
      }
      return this.paymentMethods.find(method => `${method.code}_${method.recharge_id}` === this.selectedPaymentMethod);
    },

    // 是否需要显示银行选择 - 根据channel_type判断
    // requiresBankCode() {
    //   return this.selectedPaymentMethodDetails && this.selectedPaymentMethodDetails.channel_type === 'watch_pay';
    // },

    // 获取选择的银行名称
    // selectedBankName() {
    //   if (!this.selectedBankCode || !this.bankList.length) {
    //     return 'Bank Central Asia'; // 默认显示
    //   }
    //   // 如果bankList是字符串数组，直接返回selectedBankCode
    //   if (typeof this.bankList[0] === 'string') {
    //     return this.selectedBankCode;
    //   }
    //   // 如果bankList是对象数组，查找对应的银行名称
    //   const bank = this.bankList.find(bank => bank.bank_id == this.selectedBankCode);
    //   return bank ? bank.bank : this.selectedBankCode;
    // }
  },
  
  created() {
    this.currentLanguage = localStorage['Language'] || 'en';

    // 如果有预设金额，自动填入
    if (this.$route.query.amount) {
      this.paymentAmount = this.$route.query.amount;
    }

    // 获取用户信息以显示最新余额
    this.$Model.GetUserInfo();

    // 加载支付方式
    this.loadPaymentMethods();

    // 初始化银行列表
    // this.initBankList();
  },

  beforeDestroy() {
    // 页面销毁时清理
  },

  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 获取支付方式列表
    loadPaymentMethods() {
      this.isLoading = true;

      // 使用GetPaymentTypes方法获取支付类型数据
      this.$Model.GetPaymentTypes({ country: this.selectedCountry }, (data) => {
        this.isLoading = false;

        if (data.code === 1 && data.data) {
          // 将API返回的数据转换为界面需要的格式
          this.paymentMethods = data.data.map(item => ({
            code: item.code,
            name: item.name,
            recharge_id: item.recharge_id,
            mode: item.channel_type,
            type: item.type,
            channel_type: item.channel_type,
            min_amount: item.min_amount,
            max_amount: item.max_amount,
            requires_bank_code: item.requires_bank_code
          }));

          // 默认选中第一个支付方式
          if (this.paymentMethods.length > 0 && this.paymentMethods[0].code) {
            this.selectedPaymentMethod = `${this.paymentMethods[0].code}_${this.paymentMethods[0].recharge_id}`;
          }
        } else {
          this.$Dialog.Toast(data.code_dec || this.$t('vanPull[1]'));
          this.paymentMethods = [];
        }
      });
    },

    // 选择支付方式
    selectPaymentMethod(method) {
      this.selectedPaymentMethod = method;

      // 获取选中的支付方式详情
      const selectedMethod = this.paymentMethods.find(m => `${m.code}_${m.recharge_id}` === method);

      // 如果是 watchPay 类型并且 requires_bank_code 为 true，自动设置 bankcode 为 BCA
      if (selectedMethod && selectedMethod.channel_type === 'watch_pay' && selectedMethod.requires_bank_code === true) {
        this.selectedBankCode = 'BCA';
      } else {
        // 重置银行选择
        this.selectedBankCode = '';
      }
    },

    // 格式化金额范围显示
    formatAmountRange(amount) {
      const num = Number(amount) || 0;
      // 使用简单的千位分隔符格式化
      return num.toLocaleString();
    },

    // 初始化银行列表
    // initBankList() {
    //   if (this.InitData.BanksList && this.InitData.BanksList.length > 0) {
    //     this.bankList = this.InitData.BanksList
    //       .filter(bank => bank.enabled && bank.bank.toUpperCase() !== 'USDT')
    //       .map(bank => bank.bank);
    //   } else {
    //     // 默认银行列表
    //     this.bankList = [
    //       'Bank Central Asia',
    //       'Bank Mandiri',
    //       'Bank Rakyat Indonesia',
    //       'Bank Negara Indonesia',
    //       'Bank CIMB Niaga',
    //       'Bank Danamon',
    //       'Bank Permata',
    //       'Bank Maybank Indonesia'
    //     ];
    //   }

    //   // 设置默认选择
    //   if (this.bankList.length > 0 && !this.selectedBankCode) {
    //     this.selectedBankCode = this.bankList[0];
    //   }
    // },

    // 银行选择确认
    // onBankConfirm(value, index) {
    //   // 如果bankList是字符串数组（银行名称列表）
    //   if (Array.isArray(this.bankList) && this.bankList[index]) {
    //     this.selectedBankCode = value; // 使用选中的值
    //   }
    //   this.showBankPicker = false;
    // },

    // 获取金额范围文本
    getAmountRangeText() {
      const selectedMethod = this.paymentMethods.find(m => `${m.code}_${m.recharge_id}` === this.selectedPaymentMethod);
      if (selectedMethod) {
        const min = Number(selectedMethod.min_amount);
        const max = Number(selectedMethod.max_amount);
        return `${this.$t('globalPay.amountRange')}: ${this.formatAmountRange(min)} ~ ${this.formatAmountRange(max)}`;
      }
      return '';
    },

    // 金额变化处理
    onAmountChange() {
      // 实时验证金额范围，可以在这里添加实时提示逻辑
      // 目前在提交时进行验证
    },

    // 格式化显示金额
    formatDisplayAmount(amount) {
      const num = Number(amount) || 0;
      return num.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },


    // 创建支付订单
    createPaymentOrder() {
      // 防抖：防止快速重复点击
      const now = Date.now();
      if (now - this.lastClickTime < 2000) { // 2秒内不允许重复点击
        console.log('点击过于频繁，请稍后再试');
        return;
      }
      this.lastClickTime = now;

      // 验证输入
      if (!this.selectedPaymentMethod) {
        this.$Dialog.Toast(this.$t('recharge.placeholder[1]'));
        return;
      }

      if (!this.paymentAmount) {
        this.$Dialog.Toast(this.$t('recharge.placeholder[0]'));
        return;
      }

      // 验证银行选择（如果需要）
      // 注释掉银行选择验证，因为 watchPay 类型会自动设置为 BCA
      // if (this.requiresBankCode && !this.selectedBankCode) {
      //   this.$Dialog.Toast(this.$t('globalPay.selectBank'));
      //   return;
      // }



      const amount = parseFloat(this.paymentAmount);
      const selectedMethod = this.paymentMethods.find(m => `${m.code}_${m.recharge_id}` === this.selectedPaymentMethod);

      if (selectedMethod) {
        const minAmount = Number(selectedMethod.min_amount);
        const maxAmount = Number(selectedMethod.max_amount);

        if (amount < minAmount) {
          this.$Dialog.Toast(this.$t('recharge.placeholder[3]', { currency: 'RP', min: minAmount.toLocaleString() }));
          return;
        }

        if (amount > maxAmount) {
          this.$Dialog.Toast(this.$t('recharge.placeholder[4]', { currency: 'RP', max: maxAmount.toLocaleString() }));
          return;
        }
      }

      // 获取选中的支付方式详细信息
      const selectedPaymentMethod = this.paymentMethods.find(m => `${m.code}_${m.recharge_id}` === this.selectedPaymentMethod);

      // 构建请求参数
      const orderParams = {
        country_code: this.selectedCountry,
        recharge_id: selectedPaymentMethod ? selectedPaymentMethod.recharge_id : null,
        amount: this.paymentAmount,
        currency: 'IDR'
      };

      // 只有watch_pay类型才添加pay_type
      if (selectedPaymentMethod && selectedPaymentMethod.channel_type === 'watch_pay') {
        orderParams.pay_type = selectedPaymentMethod.code;
      }
      // 只有watch_pay类型并且requires_bank_code为true才传bank_code
      if (selectedPaymentMethod && selectedPaymentMethod.channel_type === 'watch_pay' && selectedPaymentMethod.requires_bank_code === true) {
        orderParams.bank_code = 'BCA';
      }



      // 调用统一支付API创建支付订单
      this.$Model.CreateUnifiedOrder(orderParams, (data) => {
        console.log('CreateUnifiedOrder 返回数据:', data); // 调试日志

        if (data.code === 1 && data.data) {
          // 检查是否有支付URL
          if (data.data.pay_url) {
            console.log('跳转到支付URL:', data.data.pay_url); // 调试日志
            // 尝试多种跳转方式
            try {
              window.location.href = data.data.pay_url;
            } catch (error) {
              console.error('window.location.href 跳转失败:', error);
              // 备用方案
              window.open(data.data.pay_url, '_blank');
            }
          } else if (data.data.order_no) {
            // 跳转到充值订单详情页
            this.$router.push({
              name: 'watchPayOrder',
              params: { orderNumber: data.data.order_no }
            });
          } else {
            this.$Dialog.Toast(this.$t('globalPay.messages.orderCreated'));
          }
        } else {
          this.$Dialog.Toast(data.code_dec || data.msg || this.$t('globalPay.messages.paymentFailed'));
        }
      });
    }
  }
};
</script>

<style scoped>
/* 页面整体样式 */
.watch-pay-page {
  background: linear-gradient(180deg, #F42937 0%, white 30%);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}



/* 导航栏样式 */
.watch-pay-navbar {
  color: white !important;
}

.watch-pay-navbar .van-nav-bar__title {
  color: white !important;
  font-size: 20px !important;
  font-weight: 600 !important;
}

.watch-pay-navbar .van-nav-bar__arrow {
  color: white !important;
}

/* 主要内容区域 */
.WatchPay {
  padding: 60px 24px 100px; /* 为固定导航栏留出空间 */
  background: transparent;
}

/* 金额显示卡片 */
.amount-display-card {
  background: url('~@/static/images/Subtract.png');
  background-size: 100%;
  border-radius: 16px;
  padding: 20px 24px;
  margin-bottom: 24px;
  color: white;
  position: relative;
  overflow: hidden;
}

.amount-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
  font-weight: 500;
}

.amount-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 12px;
  letter-spacing: -1px;
}

.amount-subtitle {
  font-size: 14px;
  opacity: 0.8;
  line-height: 1.4;
  margin-bottom: 0;
}


/* 支付方式选择区域 */
.payment-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2D3748;
  margin-bottom: 16px;
}

/* 支付方式水平滚动容器 */
.payment-methods-scroll {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 4px 0;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.payment-methods-scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.payment-method-item {
  background: white;
  border: 2px solid #E2E8F0;
  border-radius: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* 防止收缩 */
  text-align: center;
}

.payment-method-item:hover {
  border-color: #FF0F23;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.15);
}

.payment-method-item.active {
  border-color: #FF0F23;
  background: #FFF5F5;
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.2);
}

.method-check {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: #FF0F23;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.method-name {
  font-size: 14px;
  font-weight: 600;
  color: #2D3748;
  line-height: 1.3;
}



/* 金额输入区域 */
.amount-section {
  margin-bottom: 24px;
}

.amount-input-container {
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #FF0F23;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 12px;
  transition: border-color 0.2s ease;
}

.amount-input-container:focus-within {
  border-color: #FF0F23;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.currency-prefix {
  background: #F7FAFC;
  padding: 16px 20px;
  font-weight: 600;
  color: #4A5568;
  border-right: 1px solid #E2E8F0;
  font-size: 16px;
}

.amount-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 16px 20px;
  font-size: 18px;
  font-weight: 600;
  color: #2D3748;
  background: transparent;
}

.amount-input::placeholder {
  color: #A0AEC0;
  font-weight: 500;
}

.amount-range-info {
  font-size: 14px;
  color: #FF0F23;
  background: #FFF5F5;
  padding: 12px 16px;
  border-radius: 8px;
  border-left: 4px solid #FF0F23;
}

/* 支付按钮区域 */
.payment-button-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px 24px 32px;
  background: white;
  border-top: 1px solid #E2E8F0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.pay-now-button {
  width: 100%;
  height: 56px;
  background-color: #FF0F23;
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pay-now-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(229, 62, 62, 0.5);
}

.pay-now-button:active:not(:disabled) {
  transform: translateY(0);
}

.pay-now-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}



/* 响应式设计 */
@media screen and (max-width: 375px) {
  .WatchPay {
    padding: 60px 16px 100px; /* 为固定导航栏留出空间 */
  }

  .page-title-section {
    padding: 20px 16px 30px;
  }

  .main-title {
    font-size: 24px;
  }

  .amount-display-card {
    padding: 20px;
  }

  .amount-value {
    font-size: 28px;
  }

  .payment-methods-grid {
    gap: 8px;
  }

  .payment-method-item {
    padding: 12px 8px;
    min-height: 70px;
  }

  .method-name {
    font-size: 13px;
  }

  .payment-button-section {
    padding: 16px 16px 28px;
  }

  .pay-now-button {
    height: 52px;
    font-size: 15px;
  }
}

/* 银行选择样式 */
.bank-selection-section {
  margin: 20px 0;
}

.bank-selector {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bank-selector:hover {
  border-color: #e53e3e;
}

.selected-bank {
  color: #333;
  font-weight: 500;
}

.placeholder {
  color: #999;
}

.arrow-icon {
  color: #666;
  font-size: 16px;
}
/* 下拉框弹窗样式 - 从提现页面移植 */
.PickerPopup {
  background-color: white;
  padding: 16px;
  margin-top: 20px;
  background: #fff url("../../../static/images/NoticePopup.png") no-repeat;
  background-size: 100%;
  border-radius: 32px 32px 0 0 !important;
}

.PickerPopup >>> .van-picker__toolbar{
  height: 64px!important;
}

.PickerPopup .van-picker {
  background-color: transparent;
}

.PickerPopup .van-picker__mask {
  background: none;
}

.PickerPopup .van-picker-column__item {
  font-size: 0.8rem;
  color: #2c2c2c;
}

.PickerPopup .van-picker__confirm,
.PickerPopup .van-picker__cancel {
  color: white;
}

</style>
