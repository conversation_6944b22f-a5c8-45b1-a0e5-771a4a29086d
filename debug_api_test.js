// API调试测试脚本
// 用于测试getUserInfo和getStatisticsInfo接口的差异

console.log('=== API调试测试开始 ===');

// 模拟测试两个接口的调用
function testAPIs() {
    console.log('测试getUserInfo接口...');
    
    // 检查Model对象是否可用
    if (typeof $Model !== 'undefined') {
        // 测试getUserInfo
        $Model.GetUserInfo((data) => {
            console.log('✅ getUserInfo响应:', data);
            
            // 然后测试getStatisticsInfo
            console.log('测试getStatisticsInfo接口...');
            $Model.GetStatisticsInfo((data) => {
                console.log('📊 getStatisticsInfo响应:', data);
                
                // 比较两个响应
                console.log('=== 接口比较完成 ===');
            });
        });
    } else {
        console.log('❌ $Model未定义，请在浏览器控制台中运行此测试');
    }
}

// 在页面加载完成后执行测试
if (typeof window !== 'undefined') {
    window.testAPIs = testAPIs;
    console.log('💡 请在浏览器控制台中运行: testAPIs()');
} else {
    console.log('❌ 此脚本需要在浏览器环境中运行');
}