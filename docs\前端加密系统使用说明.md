# 前端加密系统使用说明

## 系统概述

本系统实现了完整的RSA+AES混合加密架构，所有加密逻辑集中在 `CryptoManager.js` 文件中，便于代码混淆保护。

## 核心文件

### CryptoManager.js
- **位置**: `src/common/CryptoManager.js`
- **功能**: 统一加密管理器，支持代码混淆
- **特点**: 所有加密相关代码集中在一个文件中

## 加密流程

```
1. 前端生成随机AES密钥 + IV + 盐值
2. 原始数据 + 盐值 → AES加密
3. AES密钥 → RSA公钥加密
4. 发送: { key, data, iv, salt_signature }
5. 后端RSA私钥解密AES密钥
6. AES密钥解密数据
7. 验证盐值签名和唯一性
8. 处理业务逻辑
```

## 配置说明

### 启用/禁用加密

```javascript
// 方法1: localStorage控制
localStorage.setItem('CRYPTO_ENABLED', 'true')  // 启用
localStorage.setItem('CRYPTO_ENABLED', 'false') // 禁用

// 方法2: URL参数控制（测试用）
// 访问: http://localhost:8080?crypto=true

// 方法3: 代码控制（在Vue组件中）
this.$CryptoManager.setEncryptionEnabled(true)
```

### 默认启用规则
- HTTPS环境: 自动启用
- HTTP环境: 默认禁用
- 可通过localStorage或URL参数覆盖

## API加密策略

### 白名单模式（默认所有API加密）
不加密的API列表：
- `Common/BackData` - 基础配置
- `crypto/public-key` - 获取公钥
- `sms/smsCode` - 验证码图片
- `User/UploadImg` - 文件上传

### 自定义加密策略
修改 `CryptoManager.js` 中的 `shouldEncrypt` 方法：

```javascript
shouldEncrypt(apiPath) {
  // 自定义加密逻辑
  const excludeApis = ['your/api/path']
  return !excludeApis.some(api => apiPath.includes(api))
}
```

## 测试功能

### 测试页面
访问测试页面查看加密状态和测试功能：
- 路径: `/crypto-test`
- 功能: 查看加密状态、测试加密通信

### 在Vue组件中使用
```javascript
// 查看加密状态
console.log(this.$CryptoManager.getStatus())

// 启用加密
this.$CryptoManager.setEncryptionEnabled(true)

// 刷新公钥
await this.$CryptoManager.refreshPublicKey()

// 测试加密
const result = await this.$CryptoManager.encryptData({test: 'data'})
console.log(result)
```

### 在非Vue环境中使用
```javascript
// 直接导入使用
import CryptoManager from '@/common/CryptoManager'

// 使用方法同上
const result = await CryptoManager.encryptData({test: 'data'})
```

## 错误处理

### 加密失败降级
- 加密失败时自动使用原始数据
- 在控制台输出警告信息
- 不影响正常业务流程

### 公钥获取失败
- 自动重试机制
- 降级到模拟公钥（开发环境）
- 24小时自动刷新

## 性能优化

### 缓存机制
- RSA公钥缓存24小时
- 自动刷新定时器
- 内存缓存优化

### 数据大小
- 适合中小型数据加密
- 大文件建议使用专门的上传接口

## 安全特性

### 防重放攻击
- 每次请求生成唯一盐值
- 盐值包含时间戳和随机数
- 服务端验证盐值唯一性

### 数据完整性
- SHA256签名验证
- 盐值签名防篡改
- 密钥随机生成

## 部署注意事项

### 生产环境
1. **代码混淆**: 对 `CryptoManager.js` 进行混淆
2. **HTTPS**: 确保使用HTTPS传输
3. **公钥配置**: 配置固定的RSA密钥对

### 开发环境
1. **调试模式**: 可通过URL参数启用加密测试
2. **模拟数据**: 自动降级到模拟公钥
3. **详细日志**: 控制台输出详细加密信息

## 常见问题

### Q: 如何在开发环境测试加密？
A: 访问 `http://localhost:8080?crypto=true` 或设置 `localStorage.setItem('CRYPTO_ENABLED', 'true')`

### Q: 加密失败怎么办？
A: 系统会自动降级使用原始数据，不影响业务流程

### Q: 如何添加新的不加密API？
A: 在 `CryptoManager.js` 的 `shouldEncrypt` 方法中添加到 `excludeApis` 数组

### Q: 如何查看加密状态？
A: 使用 `window.CryptoManager.getStatus()` 或访问测试页面

## 代码混淆建议

### 混淆目标
- 主要混淆 `CryptoManager.js` 文件
- 保护加密算法和密钥处理逻辑
- 混淆方法名和变量名

### 混淆工具
- 推荐使用 JavaScript Obfuscator
- 配置高强度混淆选项
- 保留必要的导出接口

### 混淆配置示例
```javascript
{
  compact: true,
  controlFlowFlattening: true,
  deadCodeInjection: true,
  stringArray: true,
  stringArrayEncoding: ['base64'],
  transformObjectKeys: true
}
```

## 监控和日志

### 加密操作日志
- 公钥获取成功/失败
- 加密操作成功/失败
- 降级处理记录

### 性能监控
- 加密操作耗时
- 公钥刷新频率
- 错误率统计

## 更新和维护

### 版本更新
- 保持向后兼容性
- 渐进式升级策略
- 详细的变更日志

### 密钥轮换
- 支持RSA密钥定期更换
- 平滑的密钥过渡机制
- 自动化密钥管理

---

**注意**: 本系统的所有加密逻辑都集中在 `CryptoManager.js` 文件中，便于进行代码混淆保护。在生产环境部署时，请确保对该文件进行充分的混淆处理。
