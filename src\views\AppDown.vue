<template>
  <div class="app-download-page">
    <!-- 返回按钮 -->
    <div class="back-button">
      <van-icon name="arrow-left" size="20" @click="$router.go(-1)" />
      <span class="titleClass">{{ $t("appDown[0]") }}</span>
    </div>
    <img
      class="app-download-image"
      src="@/static/images/appDownLoad.png"
      alt=""
    />
    <div class="appButton" @click="goAndroidDown">
      <span>Android</span>
      <img class="appIcon" src="@/static/icon/androidIcon.png" alt="" />
    </div>
    <div class="appButton" @click="goIOSDown">
      <span>IOS</span>
      <img class="appIcon" src="@/static/icon/iosIcon.png" alt="" />
    </div>
  </div>
</template>
<script>
export default {
  name: "AppDown",
  data() {
    return {};
  },
  methods: {
    goAndroidDown() {
      const url = "https://www.lotteup.com/download/app.apk";

      console.log(url);
      // 当前页面跳转
      window.location.href = url;
    },
    goIOSDown() {
      const url = "https://www.lotteup.com/download/app.mobileconfig";
      console.log(url);
      // 当前页面跳转
      window.location.href = url;
    },
  },
};
</script>
<style scoped>
.app-download-page {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #f42937 0%, white 45%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0px 20px;
}
.back-button {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  position: relative;
}
.back-button .titleClass {
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0%);
  font-size: 18px;
  color: black;
}
.app-download-image {
  width: 90%;
  margin-top: 20px;
}
.appButton {
  width: 100%;
  height: 80px;
  border: 1px solid #ff0f23;
  border-radius: 12px;
  margin-top: 39px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.appButton .appIcon {
  width: 78px;
  height: 78px;
  position: absolute;
  right: 10px;
}
.appButton span {
  color: #292929;
  font-weight: bold;
  font-size: 25px;
}
</style>
