<template>
  <div class="crypto-test">
    <van-nav-bar title="加密测试" left-arrow @click-left="$router.go(-1)" />
    
    <div class="test-container">
      <!-- 加密状态 -->
      <van-cell-group title="会话制加密状态">
        <van-cell title="加密功能" :value="cryptoStatus.enabled ? '已启用' : '已禁用'" />
        <van-cell title="RSA公钥" :value="cryptoStatus.hasPublicKey ? '已获取' : '未获取'" />
        <van-cell title="公钥过期" :value="cryptoStatus.keyExpired ? '是' : '否'" />
        <van-cell title="加密会话" :value="cryptoStatus.hasSession ? '已建立' : '未建立'" />
        <van-cell title="会话过期" :value="cryptoStatus.sessionExpired ? '是' : '否'" />
        <van-cell title="系统就绪" :value="cryptoStatus.isReady ? '✅ 就绪' : '❌ 未就绪'" />
      </van-cell-group>

      <!-- 控制按钮 -->
      <van-cell-group title="控制操作">
        <van-cell>
          <template #title>
            <van-button 
              type="primary" 
              size="small" 
              @click="toggleEncryption"
            >
              {{ cryptoStatus.enabled ? '禁用加密' : '启用加密' }}
            </van-button>
          </template>
        </van-cell>
        <van-cell>
          <template #title>
            <van-button 
              type="info" 
              size="small" 
              @click="refreshPublicKey"
            >
              刷新公钥
            </van-button>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 测试区域 -->
      <van-cell-group title="加密测试">
        <van-field
          v-model="testData"
          label="测试数据"
          type="textarea"
          placeholder="输入要测试的JSON数据"
          rows="3"
        />
        <van-cell>
          <template #title>
            <van-button 
              type="success" 
              size="small" 
              @click="testEncryption"
              :loading="testing"
            >
              测试加密通信
            </van-button>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 测试结果 -->
      <van-cell-group title="测试结果" v-if="testResult">
        <van-cell title="测试状态" :value="testResult.success ? '✅ 成功' : '❌ 失败'" />
        <van-cell title="响应时间" :value="testResult.duration + 'ms'" />
        <van-cell title="前端加密" :value="testResult.encrypted ? '✅ 成功' : '❌ 失败'" />
        <van-cell title="后端解密" :value="testResult.success ? '✅ 成功' : '❌ 失败'" />
        
        <!-- 显示加密数据包详情（如果加密成功） -->
        <van-cell v-if="testResult.encryptResult" title="加密数据包" is-link @click="showEncryptDetails = !showEncryptDetails" />
        <van-collapse v-if="testResult.encryptResult && showEncryptDetails">
          <van-collapse-item title="加密详情">
            <van-field
              label="RSA加密密钥"
              type="textarea"
              :value="testResult.encryptResult.key ? testResult.encryptResult.key.substring(0, 100) + '...' : 'N/A'"
              readonly
              rows="2"
            />
            <van-field
              label="AES加密数据"
              type="textarea"
              :value="testResult.encryptResult.data ? testResult.encryptResult.data.substring(0, 100) + '...' : 'N/A'"
              readonly
              rows="2"
            />
            <van-field
              label="IV (Base64)"
              :value="testResult.encryptResult.iv || 'N/A'"
              readonly
            />
            <van-field
              label="盐值签名"
              type="textarea"
              :value="testResult.encryptResult.salt_signature ? testResult.encryptResult.salt_signature.substring(0, 50) + '...' : 'N/A'"
              readonly
              rows="2"
            />
          </van-collapse-item>
        </van-collapse>
        
        <van-field
          label="后端响应"
          type="textarea"
          :value="JSON.stringify(testResult.data, null, 2)"
          readonly
          rows="8"
        />
      </van-cell-group>

      <!-- 日志区域 -->
      <van-cell-group title="操作日志">
        <div class="log-container">
          <div 
            v-for="(log, index) in logs" 
            :key="index" 
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </van-cell-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CryptoTest',
  data() {
    return {
      cryptoStatus: {
        enabled: false,
        hasPublicKey: false,
        keyExpired: true
      },
      testData: '{"username": "test", "password": "123456", "timestamp": ' + Date.now() + '}',
      testing: false,
      testResult: null,
      showEncryptDetails: false,
      logs: []
    }
  },
  mounted() {
    this.updateCryptoStatus()
    this.addLog('页面加载完成', 'info')
  },
  methods: {
    // 更新加密状态
    updateCryptoStatus() {
      if (this.$CryptoManager) {
        this.cryptoStatus = this.$CryptoManager.getStatus()
      }
    },

    // 切换加密功能
    toggleEncryption() {
      if (this.$CryptoManager) {
        const newState = !this.cryptoStatus.enabled
        this.$CryptoManager.setEncryptionEnabled(newState)
        this.updateCryptoStatus()
        this.addLog(`加密功能已${newState ? '启用' : '禁用'}`, 'info')
      }
    },

    // 刷新公钥
    async refreshPublicKey() {
      try {
        this.addLog('开始刷新RSA公钥...', 'info')
        if (this.$CryptoManager) {
          await this.$CryptoManager.refreshPublicKey()
          this.updateCryptoStatus()
          this.addLog('RSA公钥刷新成功', 'success')
        }
      } catch (error) {
        this.addLog('RSA公钥刷新失败: ' + error.message, 'error')
      }
    },

    // 测试加密通信
    async testEncryption() {
      this.testing = true
      const startTime = Date.now()
      
      try {
        this.addLog('开始测试加密通信...', 'info')
        
        // 解析测试数据
        let data
        try {
          data = JSON.parse(this.testData)
        } catch (e) {
          throw new Error('测试数据格式错误，请输入有效的JSON')
        }

        this.addLog('原始数据: ' + JSON.stringify(data), 'info')

        // 直接测试加密功能
        if (this.$CryptoManager) {
          this.addLog('测试前端加密功能...', 'info')
          const encryptResult = await this.$CryptoManager.encryptData(data)
          
          if (encryptResult.encrypted === 'true') {
            this.addLog('✅ 前端加密成功', 'success')
            this.addLog('加密数据包字段: ' + Object.keys(encryptResult).join(', '), 'info')
            
            // 发送加密数据到后端测试解密
            this.addLog('发送到后端测试解密...', 'info')
            const response = await this.$axios.post('crypto/testEncryption', encryptResult, {
              noEncrypt: true // 这个请求本身不要再加密
            })
            
            const duration = Date.now() - startTime
            
            this.testResult = {
              success: response.data.code === 1,
              duration: duration,
              encrypted: true,
              data: response.data,
              encryptResult: encryptResult
            }

            if (response.data.code === 1) {
              this.addLog('🎉 后端解密成功！前后端加密完全兼容', 'success')
            } else {
              this.addLog('❌ 后端解密失败: ' + response.data.msg, 'error')
            }
          } else {
            this.addLog('❌ 前端加密失败: ' + (encryptResult.error || '未知错误'), 'error')
            this.testResult = {
              success: false,
              duration: Date.now() - startTime,
              encrypted: false,
              data: encryptResult
            }
          }
        } else {
          throw new Error('CryptoManager未初始化')
        }

      } catch (error) {
        const duration = Date.now() - startTime
        this.testResult = {
          success: false,
          duration: duration,
          encrypted: false,
          data: { error: error.message }
        }
        this.addLog('加密通信测试异常: ' + error.message, 'error')
      } finally {
        this.testing = false
      }
    },

    // 添加日志
    addLog(message, type = 'info') {
      const now = new Date()
      const time = now.toLocaleTimeString()
      this.logs.unshift({
        time,
        message,
        type
      })
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    }
  }
}
</script>

<style scoped>
.crypto-test {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.test-container {
  padding: 16px;
}

.van-cell-group {
  margin-bottom: 16px;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.log-time {
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.info .log-message {
  color: #333;
}

.log-item.success .log-message {
  color: #07c160;
}

.log-item.error .log-message {
  color: #ee0a24;
}
</style>
