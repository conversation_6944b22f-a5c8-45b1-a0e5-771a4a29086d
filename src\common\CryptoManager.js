import CryptoJS from 'crypto-js'
import JSEncrypt from 'jsencrypt'

/**
 * 统一加密管理器
 * 集成AES + RSA混合加密方案
 * 支持代码混淆保护
 */
class CryptoManager {
  constructor() {
    // RSA公钥缓存
    this.rsaPublicKey = null
    this.keyExpireTime = null
    this.keyRefreshTimer = null
    
    // 会话管理
    this.sessionId = null
    this.sessionKey = null // AES会话密钥
    this.sessionExpireTime = null
    this.sessionRefreshTimer = null
    
    // 配置项
    this.config = {
      // 是否启用加密（可通过环境变量或localStorage控制）
      enabled: this.getEncryptionEnabled(),
      // 公钥有效期（24小时）
      keyValidDuration: 24 * 60 * 60 * 1000,
      // 会话有效期（2小时）
      sessionValidDuration: 2 * 60 * 60 * 1000,
      // AES加密配置
      aes: {
        keySize: 32,       // 256位密钥 = 32字节
        ivSize: 16,        // 128位IV = 16字节
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      },
      // RSA加密配置
      rsa: {
        keySize: 2048 // 2048位RSA密钥
      }
    }
    
    // 初始化
    this.init()
  }

  /**
   * 获取加密启用状态
   */
  getEncryptionEnabled() {
    // 优先级：localStorage > URL参数 > 环境变量 > 默认值
    const localStorageValue = localStorage.getItem('CRYPTO_ENABLED')
    if (localStorageValue !== null) {
      return localStorageValue === 'true'
    }

    // URL参数控制（用于测试）
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.has('crypto')) {
      const cryptoParam = urlParams.get('crypto')
      return cryptoParam === 'true' || cryptoParam === '1'
    }

    // 环境变量控制
    if (typeof process !== 'undefined' && process.env) {
      return process.env.NODE_ENV === 'production'
    }

    // 默认：启用加密用于测试执行顺序
    return true
  }

  /**
   * 初始化加密管理器
   */
  async init() {
    console.log('[CryptoManager] 开始初始化加密管理器')

    // 检查依赖是否可用
    if (typeof window === 'undefined' || !window.axios) {
      console.warn('[CryptoManager] axios未准备好，延迟初始化')
      // 延迟重试，最多重试5次
      if (!this.initRetryCount) this.initRetryCount = 0
      if (this.initRetryCount < 5) {
        this.initRetryCount++
        setTimeout(() => this.init(), 500)
      }
      return
    }

    if (!this.config.enabled) {
      console.log('[CryptoManager] 加密功能已禁用（开发环境）')
      return
    }

    try {
      // 1. 获取RSA公钥
      await this.refreshPublicKey()
      // 2. 建立加密会话
      await this.establishSession()
      // 3. 启动定时器
      this.startKeyRefreshTimer()
      this.startSessionRefreshTimer()
      console.log('[CryptoManager] 初始化成功')
    } catch (error) {
      console.error('[CryptoManager] 初始化失败:', error)
    }
  }

  /**
   * 获取RSA公钥
   */
  async fetchPublicKey() {
    return new Promise((resolve, reject) => {
      // 检查axios是否可用
      if (typeof window === 'undefined' || !window.axios) {
        console.warn('[CryptoManager] axios未初始化，使用模拟公钥')
        this.useMockPublicKey(resolve)
        return
      }

      // 使用正确的端点获取公钥
      window.axios.get('/crypto/getPublicKey', { noEncrypt: true, noLogin: true })
        .then(response => {
          const data = response.data
          if (data.code === 1 && data.data && data.data.public_key) {
            console.log('[CryptoManager] 成功获取RSA公钥')
            resolve(data.data.public_key)
          } else {
            console.warn('[CryptoManager] 后端返回错误，使用模拟公钥:', data.msg)
            this.useMockPublicKey(resolve)
          }
        })
        .catch(error => {
          console.warn('[CryptoManager] 获取公钥失败，使用模拟公钥:', error.message)
          this.useMockPublicKey(resolve)
        })
    })
  }

  /**
   * 使用模拟公钥（开发环境降级方案）
   */
  useMockPublicKey(resolve) {
    const mockPublicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4f5wg5l2hKsTeNem/V41
fGnJm6gOdrj8ym3rFkEjWT2btf02uSumOiTIumsxvjvlXyObVy7uyiJ6F4Q+F2+a
****************************************************************
OgYhxid2b/4BrLDskdqJmBaAcWu4T6ZVoYMakjRCadz8R5BSHjJmn/jf9F6B5n3D
KzSYetiWSrXwKEAHKdOiRCi4k9kkdOFHHZQPNUbVYsqLdSKZXHellvMXKrWxVDFo
1aMHvzOKrfyYVaNNsz0w6cwBhH6LeRHEFBEyNyuCjn04RdQWE9ClUQhLcyZhwxaE
WQIDAQAB
-----END PUBLIC KEY-----`

    console.log('[CryptoManager] 使用模拟RSA公钥（开发环境）')
    setTimeout(() => resolve(mockPublicKey), 100)
  }

  /**
   * 刷新RSA公钥
   */
  async refreshPublicKey() {
    try {
      this.rsaPublicKey = await this.fetchPublicKey()
      this.keyExpireTime = Date.now() + this.config.keyValidDuration
      console.log('[CryptoManager] RSA公钥已更新')
    } catch (error) {
      console.error('[CryptoManager] 获取RSA公钥失败:', error)
      throw error
    }
  }

  /**
   * 建立加密会话
   */
  async establishSession() {
    try {
      console.log('[CryptoManager] 开始建立加密会话')
      
      if (!this.rsaPublicKey) {
        throw new Error('RSA公钥未准备好')
      }

      // 1. 生成32字节AES会话密钥
      this.sessionKey = this.generateAESKey()
      console.log('[CryptoManager] 生成AES会话密钥:', this.sessionKey.sigBytes, '字节')

      // 2. 用RSA公钥加密AES会话密钥
      const aesKeyBytes = this.sessionKey.toString(CryptoJS.enc.Latin1)
      const encryptedSessionKey = this.encryptWithRSA(aesKeyBytes)

      // 3. 发送到后端建立会话
      const response = await window.axios.post('/crypto/establishSession', {
        encrypted_session_key: encryptedSessionKey
      }, {
        noEncrypt: true,
        noLogin: true
      })

      if (response.data.code === 1 && response.data.data.session_id) {
        this.sessionId = response.data.data.session_id
        this.sessionExpireTime = Date.now() + this.config.sessionValidDuration
        console.log('[CryptoManager] 会话建立成功, Session ID:', this.sessionId)
        console.log('[CryptoManager] 会话有效期至:', new Date(this.sessionExpireTime).toLocaleString())
      } else {
        throw new Error('建立会话失败: ' + (response.data.msg || '未知错误'))
      }
    } catch (error) {
      console.error('[CryptoManager] 建立会话失败:', error)
      throw error
    }
  }

  /**
   * 检查会话是否有效
   */
  isSessionValid() {
    return this.sessionId && 
           this.sessionKey && 
           this.sessionExpireTime && 
           Date.now() < this.sessionExpireTime
  }

  /**
   * 启动会话自动刷新定时器
   */
  startSessionRefreshTimer() {
    // 清除现有定时器
    if (this.sessionRefreshTimer) {
      clearInterval(this.sessionRefreshTimer)
    }
    
    // 每10分钟检查一次会话是否需要续期
    this.sessionRefreshTimer = setInterval(() => {
      if (!this.isSessionValid()) {
        console.log('[CryptoManager] 会话过期，尝试重新建立')
        this.establishSession().catch(error => {
          console.error('[CryptoManager] 自动续期会话失败:', error)
        })
      }
    }, 10 * 60 * 1000) // 10分钟
  }

  /**
   * 启动公钥自动刷新定时器
   */
  startKeyRefreshTimer() {
    // 清除现有定时器
    if (this.keyRefreshTimer) {
      clearInterval(this.keyRefreshTimer)
    }
    
    // 每小时检查一次公钥是否需要刷新
    this.keyRefreshTimer = setInterval(() => {
      if (Date.now() >= this.keyExpireTime) {
        this.refreshPublicKey().catch(error => {
          console.error('[CryptoManager] 自动刷新公钥失败:', error)
        })
      }
    }, 60 * 60 * 1000) // 1小时
  }

  /**
   * 生成随机AES密钥（使用Web Crypto API确保32字节）
   */
  generateAESKey() {
    // 使用Web Crypto API生成真正的32字节随机密钥
    const keyBytes = crypto.getRandomValues(new Uint8Array(32))

    // 转换为CryptoJS WordArray格式
    const keyWordArray = CryptoJS.lib.WordArray.create(keyBytes)

    // 验证长度
    console.log(`[CryptoManager] AES密钥生成: ${keyBytes.length}字节, WordArray字节数: ${keyWordArray.sigBytes}`)

    return keyWordArray
  }

  /**
   * 生成随机IV（使用Web Crypto API确保16字节）
   */
  generateIV() {
    // 使用Web Crypto API生成真正的16字节随机IV
    const ivBytes = crypto.getRandomValues(new Uint8Array(16))

    // 转换为CryptoJS WordArray格式
    const ivWordArray = CryptoJS.lib.WordArray.create(ivBytes)

    // 验证长度
    console.log(`[CryptoManager] IV生成: ${ivBytes.length}字节, WordArray字节数: ${ivWordArray.sigBytes}`)

    return ivWordArray
  }

  /**
   * 将IV转换为正确的Base64格式
   */
  ivToBase64(iv) {
    // 将WordArray转换为Uint8Array
    const ivBytes = new Uint8Array(iv.sigBytes)
    const words = iv.words

    for (let i = 0; i < iv.sigBytes; i++) {
      const byte = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff
      ivBytes[i] = byte
    }

    // 使用标准的btoa方法转换为Base64
    const ivBase64 = btoa(String.fromCharCode(...ivBytes))

    console.log(`[CryptoManager] IV转换: ${ivBytes.length}字节 -> Base64长度: ${ivBase64.length}`)

    return ivBase64
  }

  /**
   * 生成随机盐值（与后端格式兼容）
   */
  generateSalt() {
    // 使用md5(uniqid(mt_rand(), true))的等效实现
    const timestamp = Date.now().toString()
    const random = Math.floor(Math.random() * 1000000)
    const randomString = CryptoJS.lib.WordArray.random(16).toString()
    const saltString = timestamp + random + randomString
    return CryptoJS.MD5(saltString).toString()
  }

  /**
   * AES加密数据
   */
  encryptWithAES(data, key, iv) {
    try {
      const encrypted = CryptoJS.AES.encrypt(data, key, {
        iv: iv,
        mode: this.config.aes.mode,
        padding: this.config.aes.padding
      })
      // 后端使用OPENSSL_RAW_DATA标志，期望base64字符串
      // CryptoJS默认的toString()就是base64格式，这是正确的
      return encrypted.toString()
    } catch (error) {
      console.error('[CryptoManager] AES加密失败:', error)
      throw error
    }
  }

  /**
   * RSA加密AES密钥
   */
  encryptWithRSA(data) {
    try {
      if (!this.rsaPublicKey) {
        throw new Error('RSA公钥未初始化')
      }
      
      const encrypt = new JSEncrypt()
      encrypt.setPublicKey(this.rsaPublicKey)
      // 如果data是WordArray，转换为字节字符串，否则直接使用
      const dataToEncrypt = typeof data === 'string' ? data : data.toString(CryptoJS.enc.Latin1)
      const encrypted = encrypt.encrypt(dataToEncrypt)
      
      if (!encrypted) {
        throw new Error('RSA加密失败')
      }
      
      return encrypted
    } catch (error) {
      console.error('[CryptoManager] RSA加密失败:', error)
      throw error
    }
  }

  /**
   * 完整的会话制加密流程
   */
  async encryptData(originalData) {
    console.log('='.repeat(80))
    console.log('🔐 [CryptoManager] 开始会话制加密')
    console.log('='.repeat(80))
    console.log('📋 原始请求数据:')
    console.log('- 数据类型:', typeof originalData)
    console.log('- 数据内容:', JSON.stringify(originalData, null, 2))
    console.log('- 数据大小:', JSON.stringify(originalData).length, '字符')

    if (!this.config.enabled) {
      console.log('❌ 加密功能未启用，返回原始数据')
      return {
        encrypted: false,
        data: originalData
      }
    }

    try {
      // 1. 检查会话是否有效
      if (!this.isSessionValid()) {
        console.log('[CryptoManager] 会话无效，尝试重新建立')
        await this.establishSession()
      }

      if (!this.isSessionValid()) {
        throw new Error('无法建立有效会话')
      }

      // 2. 生成随机IV
      const iv = this.generateIV()

      // 3. 使用会话密钥加密数据
      const dataToEncrypt = JSON.stringify(originalData)
      const encryptedData = this.encryptWithAES(dataToEncrypt, this.sessionKey, iv)

      // 4. 构造加密数据包
      const result = {
        encrypted: true,
        session_id: this.sessionId,
        data: encryptedData,
        iv: this.ivToBase64(iv)
      }

      console.log('📤 会话制加密数据包:')
      console.log('- session_id:', result.session_id)
      console.log('- data长度:', result.data.length, '字符')
      console.log('- iv:', result.iv)
      console.log('='.repeat(80))
      console.log('✅ 会话制加密完成')
      console.log('='.repeat(80))

      return result
    } catch (error) {
      console.error('[CryptoManager] 会话制加密失败:', error)
      // 加密失败时的降级处理
      return {
        encrypted: false,
        data: originalData,
        error: error.message
      }
    }
  }

  /**
   * 获取当前日期（用于密钥生成，格式：Y-m-d）
   */
  getCurrentDate() {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  /**
   * 检查是否需要加密
   */
  shouldEncrypt(apiPath) {
    // 如果加密功能未启用，直接返回false
    if (!this.config.enabled) {
      return false
    }

    // 不需要加密的API列表（白名单模式）
    const excludeApis = [
      'Common/BackData',           // 基础配置数据
      'Common/GetRSAPublicKey',    // 获取公钥本身不需要加密
      'crypto/public-key',         // 新的公钥获取端点
      'crypto/test',               // 加密测试端点
      'sms/smsCode',              // 获取验证码图片
      'User/UploadImg'            // 文件上传（使用FormData）
    ]

    // 默认所有API都需要加密，除非在排除列表中
    return !excludeApis.some(api => apiPath.includes(api))
  }

  /**
   * 动态启用/禁用加密
   */
  setEncryptionEnabled(enabled) {
    this.config.enabled = enabled
    localStorage.setItem('CRYPTO_ENABLED', enabled.toString())
    console.log(`[CryptoManager] 加密功能已${enabled ? '启用' : '禁用'}`)
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      enabled: this.config.enabled,
      hasPublicKey: !!this.rsaPublicKey,
      keyExpireTime: this.keyExpireTime,
      keyExpired: this.keyExpireTime ? Date.now() >= this.keyExpireTime : true,
      hasSession: !!this.sessionId,
      sessionExpireTime: this.sessionExpireTime,
      sessionExpired: !this.isSessionValid(),
      isReady: this.config.enabled && !!this.rsaPublicKey && this.isSessionValid()
    }
  }

  /**
   * 等待加密管理器准备就绪（包含会话）
   */
  async waitForReady(timeout = 10000) {
    const startTime = Date.now()

    while (!this.getStatus().isReady) {
      if (Date.now() - startTime > timeout) {
        throw new Error('加密管理器初始化超时（包含会话建立）')
      }
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    return true
  }

  /**
   * 销毁加密管理器
   */
  destroy() {
    if (this.keyRefreshTimer) {
      clearInterval(this.keyRefreshTimer)
      this.keyRefreshTimer = null
    }
    if (this.sessionRefreshTimer) {
      clearInterval(this.sessionRefreshTimer)
      this.sessionRefreshTimer = null
    }
    this.rsaPublicKey = null
    this.keyExpireTime = null
    this.sessionId = null
    this.sessionKey = null
    this.sessionExpireTime = null
    console.log('[CryptoManager] 已销毁')
  }
}

// 创建单例实例
const cryptoManager = new CryptoManager()

export default cryptoManager
